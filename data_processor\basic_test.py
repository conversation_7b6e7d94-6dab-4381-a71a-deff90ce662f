#!/usr/bin/env python3
"""
Basic test script to verify Python is working correctly.
"""

import os
import sys
from pathlib import Path

def main():
    """Print basic information about the environment."""
    print("Python version:", sys.version)
    print("Current working directory:", os.getcwd())
    
    # List files in the current directory
    print("\nFiles in current directory:")
    for file in Path(".").glob("*"):
        print(f"  {file}")
    
    # List files in the src directory
    print("\nFiles in src directory:")
    for file in Path("src").glob("**/*"):
        print(f"  {file}")
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    main()
