#!/usr/bin/env python3
"""
Directly process school location data without using the processor classes.
"""

import os
import sys
import pandas as pd
from pathlib import Path

def main():
    """Process school location data directly."""
    print("Processing school location data directly...")
    
    # Define paths
    data_dir = Path(__file__).parent / 'data'
    raw_dir = data_dir / 'raw'
    processed_dir = data_dir / 'processed'
    
    # Print paths
    print(f"Data directory: {data_dir}")
    print(f"Raw directory: {raw_dir}")
    print(f"Processed directory: {processed_dir}")
    
    # List files in the raw directory
    print("\nFiles in raw directory:")
    for file in raw_dir.glob("**/*.xlsx"):
        print(f"  {file.relative_to(raw_dir)}")
    
    # List files in the processed directory
    print("\nFiles in processed directory:")
    for file in processed_dir.glob("**/*.json"):
        print(f"  {file.relative_to(processed_dir)}")
    
    # Process the data
    input_file = raw_dir / "School Location 2024.xlsx"
    output_file = processed_dir / "school_locations_direct.json"
    
    print(f"\nProcessing {input_file} to {output_file}...")
    
    try:
        # Read the Excel file
        df = pd.read_excel(input_file, sheet_name=1)
        
        # Display information about the DataFrame
        print("\nFirst few rows of the Excel file:")
        print(df.head())
        print("\nShape of DataFrame:", df.shape)
        print("\nColumns in Excel file:")
        print(df.columns.tolist())
        
        # Define required columns
        required_columns = [
            'Calendar Year', 'School Name', 'School Sector', 'School Type',
            'State', 'Suburb', 'Latitude', 'Longitude', 'Postcode'
        ]
        
        # Verify all required columns exist
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Select only required columns
        df_selected = df[required_columns]
        
        # Normalize column names to lowercase with underscores
        df_selected.columns = [col.lower().replace(' ', '_') for col in df_selected.columns]
        
        # Create output directory if it doesn't exist
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Save to JSON
        df_selected.to_json(output_file, orient='records', indent=4)
        
        print("\nPreview of processed data (first 5 rows):")
        print(df_selected.head())
        print(f"\nSuccessfully processed {input_file} to {output_file}")
        print(f"Processed {len(df_selected)} school records")
        
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        return 1
    
    print("\nProcessing completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
