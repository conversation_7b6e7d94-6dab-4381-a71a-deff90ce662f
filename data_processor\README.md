# SchoolZone Data Processor

Data processing component for the SchoolZone project. Handles crawling, processing, and managing school data.

## Components

- `src/crawler/` - Web crawling functionality for school rankings
- `src/processors/` - Data processing modules for school data
  - `data_processor.py` - Base and specialized data processors for different data types
  - `pipeline.py` - Pipeline for processing multiple data sources
- `src/validation/` - Data validation utilities
- `data/` - Data storage
  - `raw/` - Original source data
  - `processed/` - Processed and validated data

## Architecture

The data processor follows a modular architecture using the Template Method and Factory patterns:

1. **BaseExcelProcessor** - Abstract base class that defines the common processing workflow
2. **Specialized Processors** - Concrete implementations for different data types:
   - `SchoolLocationProcessor` - Processes school location data
   - `EnrolmentProcessor` - Processes enrollment data
   - `NationalResultsProcessor` - Processes NAPLAN results data
   - `SchoolProfileProcessor` - Processes school profile data
3. **ProcessorFactory** - Creates the appropriate processor based on file type
4. **DataPipeline** - Coordinates processing of multiple data sources

## Setup

```bash
pip install -r requirements.txt
```

## Usage

### Run the Pipeline

The pipeline can be run using the `run_pipeline.py` script, which provides several commands:

```bash
# List available files
python -m src.run_pipeline list

# Process all Excel files
python -m src.run_pipeline process-all

# Process a single file
python -m src.run_pipeline process "School Location 2024.xlsx" --type location

# Merge processed CSV files
python -m src.run_pipeline merge

# Export to JSON
python -m src.run_pipeline export

# Run the full pipeline (process all, merge, export)
python -m src.run_pipeline full
```

### Process School Data Directly

You can also use the processors directly:

```bash
# Process school location data
python -m src.processors.data_processor
```

### Crawl School Rankings

```bash
python -m src.crawler.web_crawler
```

## Data Flow

1. Raw data is stored in `data/raw/`
2. Processors read from raw data and write to `data/processed/`
3. The pipeline can merge multiple processed files and export to JSON
4. Processed data is used by the web application
