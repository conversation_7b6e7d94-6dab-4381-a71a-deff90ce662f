#!/usr/bin/env python3
"""
Process school location data using the SchoolLocationProcessor class.
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Import the processor class
from src.processors.data_processor import SchoolLocationProcessor

def main():
    """Process school location data."""
    print("Processing school location data...")
    
    # Create a processor
    processor = SchoolLocationProcessor()
    
    # Process the data
    processor.process_data(
        input_file="School Location 2024.xlsx",
        output_file="school_locations.json"
    )
    
    print("Processing completed successfully!")

if __name__ == "__main__":
    main()
