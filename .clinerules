# SchoolZone Project Rules

## Data Processing Patterns
- Only government primary schools in Victoria are included
- Geographic data must have both lat/long coordinates and boundary polygons
- All data processing output headers must be lowercase with underscores between words (e.g. school_name, enrollment_count)
- Data pipelines are split into national and state-specific processors
- Standard validation rules applied across all data sources
- Automated backup before data updates

## File Organization
- /data_processor/ for all data processing logic
- /web_app/ for the Flask web application
- Processed data stored in data_processor/data/processed/
- Raw data maintained in data_processor/data/raw/
- Source code separated by functionality in src/ directories
- Clear separation between national and state-specific data

## Code Style
- Python files use snake_case naming
- HTML/JavaScript uses camelCase
- Data files use underscore_separated names
- Functions should have clear, descriptive names
- Type hints required for Python functions
- Documentation required for complex functions
- Error handling required for data operations

## Development Workflow
- Data updates follow: scrape -> process -> validate -> backup -> deploy
- Frontend changes require testing at multiple zoom levels
- API responses must include proper error handling
- Geographic data requires boundary validation
- Cross-source data validation before processing
- Regular automated backups of processed data
- Test coverage required for critical paths

## API Patterns
- Endpoints use kebab-case (e.g., /api/ranked-schools)
- Query parameters use camelCase
- Geographic bounds required for data filtering
- JSON responses for all API endpoints
- Standard error response format
- Rate limiting on data-heavy endpoints
- Cache headers for static resources

## UI Patterns
- School markers use color coding for rankings
- Top 20% schools shown by default
- Dynamic marker sizing based on zoom level
- School zone polygons use semi-transparent colors
- Loading indicators for async operations
- Responsive design for all screen sizes
- Touch-friendly controls for mobile
- Clear visual hierarchy in data display

## Data Validation Rules
- School names must exactly match between datasets
- Postcodes must be valid Victorian postcodes
- Coordinates must be within Victoria bounds
- Ranking percentiles must be between 0-100%
- ICSEA scores must be valid numbers
- Enrollment data must be non-negative
- Geographic boundaries must be valid polygons
- Data timestamps must be current year

## Performance Guidelines
- Use debounced map updates (300ms)
- Implement viewport-based data loading
- Canvas rendering for better performance
- Optimize GeoJSON responses
- Progressive loading for large datasets
- Client-side caching of static data
- Server-side response compression
- Efficient boundary data transmission

## Security Guidelines
- Input validation on all API endpoints
- Sanitize data before processing
- Validate geographic boundary data
- Rate limit web crawler operations
- Secure file operation handling
- Error logging without sensitive data
- Safe third-party data handling

## Data Quality Standards
- Cross-reference school names across sources
- Validate all geographic coordinates
- Verify school zone boundaries
- Check ranking data consistency
- Validate enrollment numbers
- Monitor data freshness
- Track update frequency
- Document data anomalies

## Monitoring Requirements
- Track API response times
- Monitor data processing duration
- Log validation failures
- Track crawler performance
- Monitor system resource usage
- Alert on processing errors
- Regular backup verification
- Data freshness monitoring
