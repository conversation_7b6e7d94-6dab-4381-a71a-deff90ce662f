import os
import pandas as pd
import json
import argparse
import sys

def process_school_data(school_names=None):
    # Define file paths
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    input_file = os.path.join(base_dir, "data_processor", "data", "raw", "School Profile 2008-2024.xlsx")
    output_file = os.path.join(base_dir, "data_processor", "data", "processed", "school_profiles.json")
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file not found at {input_file}")
        return
    
    try:
        # Load the second sheet of the Excel file
        df = pd.read_excel(input_file, sheet_name=1)
        
        # We'll use a list to store all schools' data
        all_schools_data = []
        
        # If no school names provided, use default
        if not school_names:
            school_names = ["Heathmont East Primary School"]
        # If "all" is specified, process all schools
        elif school_names == ["all"]:
            school_names = df['School Name'].unique().tolist()
            print(f"Processing all {len(school_names)} schools...")
        
        for school_name in school_names:
            school_name = school_name.strip()

            # Filter by school name
            filtered_df = df[df['School Name'].str.contains(school_name, case=False, regex=True, na=False)]
            
            if filtered_df.empty:
                print(f"No records found for {school_name}")
                continue
                
            # Select required columns
            columns_mapping = {
                'School Name': 'school_name',
                'Calendar Year': 'calendar_year',
                'Bottom SEA Quarter (%)': 'sea_bottom',
                'Lower Middle SEA Quarter (%)': 'sea_lower',
                'Upper Middle SEA Quarter (%)': 'sea_upper',
                'Top SEA Quarter (%)': 'sea_top'
            }
            
            # Check if columns exist
            for col in columns_mapping.keys():
                if col not in df.columns:
                    print(f"Warning: Column '{col}' not found in Excel sheet")
            
            # Create a new DataFrame with the required columns
            result_df = filtered_df[[*columns_mapping.keys()]].rename(columns=columns_mapping)
            
            # Convert percentage columns to float to maintain decimal precision
            percentage_columns = ['sea_bottom', 'sea_lower', 'sea_upper', 'sea_top']
            for col in percentage_columns:
                result_df[col] = result_df[col].astype(float)
            
            # Adjust percentages to ensure they sum to 100
            for idx, row in result_df.iterrows():
                total = sum(row[col] for col in percentage_columns)
                if total != 100:
                    # Calculate adjustment factor
                    adjustment_factor = 100 / total
                    
                    # Apply adjustment proportionally
                    for col in percentage_columns:
                        result_df.at[idx, col] = round(row[col] * adjustment_factor, 2)
                    
                    # Handle potential rounding issues by adding/subtracting from largest value
                    new_total = sum(result_df.loc[idx, col] for col in percentage_columns)
                    if new_total != 100:
                        # Find the column with the largest value
                        largest_col = max(percentage_columns, key=lambda c: result_df.loc[idx, c])
                        # Adjust it to make the sum exactly 100
                        result_df.at[idx, largest_col] += (100 - new_total)
            
            # Add the school's data to our results list
            school_data = result_df.to_dict(orient='records')
            all_schools_data.extend(school_data)
            print(f"Processed {len(school_data)} records for {school_name}")
        
        # Write all schools' data to JSON file
        with open(output_file, 'w') as f:
            json.dump(all_schools_data, f, indent=2)
            
        print(f"Successfully exported data to {output_file}")
        print(f"Total number of records: {len(all_schools_data)}")
        
    except Exception as e:
        print(f"Error processing file: {e}")

def main():
    parser = argparse.ArgumentParser(description="Process school profile data.")
    parser.add_argument("--school", "-s", nargs="+", help="School name(s) to process. Use 'all' to process all schools.")
    args = parser.parse_args()
    
    # If school names are provided, use them
    if args.school:
        process_school_data(args.school)
    else:
        process_school_data()

if __name__ == "__main__":
    main()
