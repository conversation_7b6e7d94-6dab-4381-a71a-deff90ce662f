'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap, useMapEvents, Polygon } from 'react-leaflet'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import type { LatLngExpression, LatLngBounds } from 'leaflet'

interface School {
  coordinates: number[][][]
  school_name: string
  postcode: string
  ranking_percentile: string
  latitude: number
  longitude: number
}

const getMarkerColor = (percentile: string): string => {
  const value = parseInt(percentile)
  if (value <= 1) return '#ff0000'   // Top 1% - Red
  if (value <= 3) return '#f7831e'   // Top 2-3% - Light Orange
  if (value <= 6) return '#d0d424'   // Top 4-6% - Dark Yellow
  if (value <= 10) return '#2ecc71'  // Top 7-10% - Green
  if (value <= 15) return '#3498db'  // Top 11-15% - Blue
  if (value <= 20) return '#c722b4'  // Top 16-20% - Dark Grey
  return '#4a4a4a' // > 20% - <PERSON> (won't be shown)
}

const getMarkerSize = (zoom: number): number => {
  if (zoom >= 15) return 24
  if (zoom >= 13) return 20
  if (zoom >= 11) return 16
  return 12
}

const debounce = <F extends (...args: any[]) => any>(
  func: F,
  waitFor: number
) => {
  let timeout: NodeJS.Timeout

  return (...args: Parameters<F>): Promise<ReturnType<F>> =>
    new Promise(resolve => {
      if (timeout) {
        clearTimeout(timeout)
      }

      timeout = setTimeout(() => resolve(func(...args)), waitFor)
    })
}

const MapEvents = ({ onMoveEnd }: { onMoveEnd: (bounds: LatLngBounds) => void }) => {
  const map = useMapEvents({
    moveend: debounce(() => {
      onMoveEnd(map.getBounds())
    }, 300),
    zoomend: debounce(() => {
      onMoveEnd(map.getBounds())
    }, 300)
  })
  return null
}

const MarkerLayer = ({ schools, zoom }: { schools: School[], zoom: number }) => {
  const markerSize = getMarkerSize(zoom)

  return (
    <>
      {schools.map((school) => {
        const color = getMarkerColor(school.ranking_percentile)
        const icon = L.divIcon({
          className: 'custom-marker',
          html: `<div style="background-color: ${color}; width: ${markerSize}px; height: ${markerSize}px; border-radius: 50%; border: 2px solid white;"></div>`,
          iconSize: [markerSize, markerSize],
        })

        return (
          <Marker
            key={school.school_name}
            position={[school.latitude, school.longitude]}
            icon={icon}
          >
            <Popup>
              <div>
                <h3 className="font-bold">{school.school_name}</h3>
                <p>Ranking: Top {school.ranking_percentile}</p>
                <p>Postcode: {school.postcode}</p>
              </div>
            </Popup>
          </Marker>
        )
      })}
    </>
  )
}

const Legend = () => (
  <div className="absolute bottom-5 right-5 bg-white p-3 rounded-lg shadow-lg z-[1000]">
    <h4 className="font-bold mb-2">School Rankings</h4>
    <div className="space-y-2">
      {[
        { range: "Top 1%", color: "#ff0000" },
        { range: "Top 2-3%", color: "#f7831e" },
        { range: "Top 4-6%", color: "#d0d424" },
        { range: "Top 7-10%", color: "#2ecc71" },
        { range: "Top 11-15%", color: "#3498db" },
        { range: "Top 16-20%", color: "#c722b4" },
      ].map((item) => (
        <div key={item.range} className="flex items-center gap-2">
          <div
            style={{ backgroundColor: item.color }}
            className="w-4 h-4 border border-gray-300 rounded-full"
          />
          <span className="text-sm">{item.range}</span>
        </div>
      ))}
    </div>
  </div>
)

const MapComponent = () => {
  const center: LatLngExpression = [-37.8136, 144.9631] // Melbourne coordinates
  const [schools, setSchools] = useState<School[]>([])
  const [zoom, setZoom] = useState(11)
  const mapRef = useRef<L.Map | null>(null)

  const loadSchools = useCallback(async (bounds?: LatLngBounds) => {
    try {
      const url = new URL('/api/schools', window.location.origin)
      if (bounds) {
        url.searchParams.set('north', bounds.getNorth().toString())
        url.searchParams.set('south', bounds.getSouth().toString())
        url.searchParams.set('east', bounds.getEast().toString())
        url.searchParams.set('west', bounds.getWest().toString())
      }
      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to fetch schools')
      const data = await response.json()
      // Filter out schools below top 20%
      const filteredData = data.filter((school: School) => parseInt(school.ranking_percentile) <= 20)
      setSchools(filteredData)
    } catch (error) {
      console.error('Error loading schools:', error)
    }
  }, [])

  const handleMoveEnd = useCallback((bounds: LatLngBounds) => {
    if (mapRef.current) {
      setZoom(mapRef.current.getZoom())
      loadSchools(bounds)
    }
  }, [loadSchools])

  useEffect(() => {
    loadSchools()
  }, [loadSchools])

  return (
    <div className="w-full h-full relative">
      <MapContainer
        center={center}
        zoom={11}
        scrollWheelZoom={true}
        style={{ height: '100vh', width: '100%' }}
        ref={mapRef}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        <MapEvents onMoveEnd={handleMoveEnd} />
        <MarkerLayer schools={schools} zoom={zoom} />
        {schools.map((school) => (
          <Polygon
            key={`${school.school_name}-boundary`}
            positions={school.coordinates.map(coords => 
              coords.map(([lng, lat]) => [lat, lng] as LatLngExpression)
            )}
            pathOptions={{
              fillColor: getMarkerColor(school.ranking_percentile),
              fillOpacity: 0.2,
              weight: 2,
              opacity: 0.5,
              color: getMarkerColor(school.ranking_percentile)
            }}
          >
            <Popup>
              <div>
                <h3 className="font-bold">{school.school_name}</h3>
                <p>Ranking: Top {school.ranking_percentile}</p>
                <p>Postcode: {school.postcode}</p>
              </div>
            </Popup>
          </Polygon>
        ))}
      </MapContainer>
      <Legend />
    </div>
  )
}

export default MapComponent
