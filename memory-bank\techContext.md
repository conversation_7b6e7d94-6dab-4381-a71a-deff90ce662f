# Technical Context

## Development Environment
- Operating System: Windows 11
- Shell: PowerShell 7
- IDE: Visual Studio Code
- Python 3.x for data processing
- Node.js and npm for JavaScript development

## Technical Stack

### Data Processing
- Python 3.x
  - Pandas for data manipulation
  - NumPy for numerical operations
  - GeoPy for geographic calculations
  - BeautifulSoup for web crawling

### Frontend
- Next.js 14+ with TypeScript
- Tailwind CSS for styling
- React components
- Client-side caching
- Dynamic loading

### Development Tools
- Git for version control
- VS Code extensions
  - Python
  - TypeScript
  - Tailwind CSS
  - ESLint
  - Prettier

## Project Structure

### Data Processing (/data_processor/)
```
data_processor/
├── src/
│   ├── crawler/
│   │   └── web_crawler.py
│   ├── processors/
│   │   ├── __init__.py
│   │   ├── data_processor.py
│   │   └── pipeline.py
│   └── run_pipeline.py
├── data/
│   ├── raw/
│   └── processed/
├── main.py
└── requirements.txt
```

### Frontend (/test_frontpage/)
```
test_frontpage/
├── app/
│   ├── api/
│   │   └── schools/
│   ├── layout.tsx
│   ├── page.tsx
│   └── map-component.tsx
├── public/
│   └── data/
└── package.json
```

## Data Architecture
- Separate national and state pipelines
- Geographic data with coordinates and polygons
- Standardized headers (lowercase with underscores)
- Raw data preservation
- Processed data versioning

## Development Workflows

### 1. Data Processing
- Source data collection
- Raw data validation
- Processing pipeline execution
- Output validation
- Data backup
- Version control

### 2. Frontend Development
- Component development
- TypeScript type safety
- API integration
- Performance optimization
- Responsive design implementation

### 3. Data Pipeline Updates
- Raw data updates
- Pipeline execution
- Validation checks
- Backup creation
- Processed data update
- Frontend data sync

## Security Guidelines
- Input validation on all endpoints
- Data sanitization in processing
- Safe file operations
- Secure API endpoints
- Error logging protocols

## Performance Optimization
- Client-side caching
- Dynamic data loading
- Geographic data optimization
- Response compression
- Viewport-based rendering

## Command Line Operations
- Use PowerShell 'start' for URLs
- Working directory: c:/Personal Project/a05_schoolzone
- Pipeline execution through run_pipeline.py
- Frontend dev server with npm

## Testing Strategy
- Unit tests for processors
- Integration tests for pipeline
- Frontend component testing
- API endpoint validation
- Geographic data verification

## Deployment Considerations
- Data pipeline automation
- Frontend build optimization
- Static asset management
- API endpoint configuration
- Environmental variables

## Monitoring
- Pipeline execution logs
- Data processing metrics
- API response times
- Frontend performance
- Error tracking

## Documentation
- Code documentation
- API specifications
- Data schemas
- Processing workflows
- Setup instructions
