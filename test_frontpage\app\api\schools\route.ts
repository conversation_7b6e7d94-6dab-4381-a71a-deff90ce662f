import { NextResponse } from 'next/server'
import path from 'path'
import fs from 'fs'

interface School {
  school_name: string
  postcode: string
  ranking_percentile: string
  latitude: number
  longitude: number
  coordinates: number[][][]
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const north = parseFloat(searchParams.get('north') || '0')
    const south = parseFloat(searchParams.get('south') || '0')
    const east = parseFloat(searchParams.get('east') || '0')
    const west = parseFloat(searchParams.get('west') || '0')

    const filePath = path.join(process.cwd(), 'public', 'data', 'school_ranking_data.json')
    const fileContents = fs.readFileSync(filePath, 'utf8')
    const schools: School[] = JSON.parse(fileContents)
    
    if (north && south && east && west) {
      const filtered = schools.filter(school => 
        school.latitude <= north &&
        school.latitude >= south &&
        school.longitude <= east &&
        school.longitude >= west
      )
      return NextResponse.json(filtered)
    }

    return NextResponse.json(schools)
  } catch (error) {
    console.error('Error loading school data:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}
