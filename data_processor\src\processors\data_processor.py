import pandas as pd
import os
import json
from pathlib import Path
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union, Tuple

class BaseExcelProcessor(ABC):
    """
    Abstract base class for processing Excel data files.
    Implements the Template Method pattern for Excel file processing.
    """
    
    def __init__(self, data_dir: str = None):
        """
        Initialize the processor with data directory paths.
        
        Args:
            data_dir (str): Base directory for data files. If None, uses data_processor/data/
        """
        if data_dir is None:
            data_dir = Path(__file__).parent.parent.parent / 'data'
        self.data_dir = Path(data_dir)
        self.raw_dir = self.data_dir / 'raw'
        self.processed_dir = self.data_dir / 'processed'
        
        # Create directories if they don't exist
        self.raw_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
    
    @property
    @abstractmethod
    def required_columns(self) -> List[str]:
        """
        List of required columns for this processor.
        Must be implemented by subclasses.
        
        Returns:
            List[str]: List of column names required in the Excel file
        """
        pass
    
    @property
    def sheet_name(self) -> Union[int, str]:
        """
        Sheet name or index to read from the Excel file.
        Can be overridden by subclasses.
        
        Returns:
            Union[int, str]: Sheet name or index (0-based)
        """
        return 0  # Default to first sheet
    
    def normalize_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Convert column names to lowercase with underscores between words.
        
        Args:
            df (pd.DataFrame): DataFrame with original column names
            
        Returns:
            pd.DataFrame: DataFrame with normalized column names
        """
        df.columns = [col.lower().replace(' ', '_') for col in df.columns]
        return df
    
    def process_data(self, input_file: str, output_file: str) -> None:
        """
        Process data from Excel to JSON format.
        Template method that defines the skeleton of the algorithm.
        
        Args:
            input_file (str): Path to the input Excel file
            output_file (str): Path to the output JSON file
        """
        try:
            # Convert to Path objects and resolve relative paths
            input_path = self.raw_dir / input_file if not Path(input_file).is_absolute() else Path(input_file)
            
            # Ensure output file has .json extension
            if not output_file.endswith('.json'):
                output_file = Path(output_file).with_suffix('.json').name
                
            output_path = self.processed_dir / output_file if not Path(output_file).is_absolute() else Path(output_file)
            
            # Read the Excel file
            df = self.read_excel_file(input_path)
            
            # Display information about the DataFrame
            self.display_dataframe_info(df)
            
            # Verify all required columns exist
            self.validate_columns(df)
            
            # Transform the data (implemented by subclasses)
            df_processed = self.transform_data(df)
            
            # Normalize column names to lowercase with underscores
            df_processed = self.normalize_column_names(df_processed)
            
            # Create output directory if it doesn't exist
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Convert school_age_id to integer if it exists in the DataFrame
            if 'school_age_id' in df_processed.columns:
                # Use Int64 pandas dtype which supports NaN values
                df_processed['school_age_id'] = pd.to_numeric(df_processed['school_age_id'], errors='coerce').astype('Int64')
                
            # Save to JSON
            df_processed.to_json(output_path, orient='records', indent=4)
            
            print("\nPreview of processed data (first 5 rows):")
            print(df_processed.head())
            print(f"\nSuccessfully processed {input_path} to {output_path}")
            print(f"Processed {len(df_processed)} records")
            
        except Exception as e:
            print(f"Error processing file: {str(e)}")
            raise
    
    def read_excel_file(self, input_path: Path) -> pd.DataFrame:
        """
        Read the Excel file into a DataFrame.
        Can be overridden by subclasses for custom reading logic.
        
        Args:
            input_path (Path): Path to the input Excel file
            
        Returns:
            pd.DataFrame: DataFrame containing the Excel data
        """
        return pd.read_excel(input_path, sheet_name=self.sheet_name)
    
    def display_dataframe_info(self, df: pd.DataFrame) -> None:
        """
        Display information about the DataFrame.
        
        Args:
            df (pd.DataFrame): DataFrame to display information about
        """
        print("\nFirst few rows of the Excel file:")
        print(df.head())
        print("\nShape of DataFrame:", df.shape)
        print("\nColumns in Excel file:")
        print(df.columns.tolist())
    
    def validate_columns(self, df: pd.DataFrame) -> None:
        """
        Verify all required columns exist in the DataFrame.
        
        Args:
            df (pd.DataFrame): DataFrame to validate
            
        Raises:
            ValueError: If any required columns are missing
        """
        missing_columns = [col for col in self.required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
    
    def transform_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Transform the data before saving.
        Default implementation selects only the required columns.
        Can be overridden by subclasses for custom transformation logic.
        
        Args:
            df (pd.DataFrame): DataFrame to transform
            
        Returns:
            pd.DataFrame: Transformed DataFrame
        """
        return df[self.required_columns]


class SchoolLocationProcessor(BaseExcelProcessor):
    """Process school location data from Excel to CSV format."""
    
    @property
    def required_columns(self) -> List[str]:
        return [
            'Calendar Year', 'School AGE ID', 'School Name', 'School Sector', 'School Type', 'Special school', 'Campus Type',
            'State', 'Suburb', 'Latitude', 'Longitude', 'Postcode', 'ABS Remoteness Area Name', 'Local Government Area Name'
        ]
    
    @property
    def sheet_name(self) -> int:
        return 1  # School location data is on the second sheet


class EnrolmentProcessor(BaseExcelProcessor):
    """Process school enrolment data from Excel to CSV format."""
    
    @property
    def required_columns(self) -> List[str]:
        return [
            'Calendar Year', 'School Name', 'State', 'Total Enrolments'
        ]
    
    def transform_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Transform enrolment data, potentially calculating total enrolments
        from grade-specific columns if needed.
        
        Args:
            df (pd.DataFrame): DataFrame to transform
            
        Returns:
            pd.DataFrame: Transformed DataFrame
        """
        # Check if 'Total Enrolments' exists, if not calculate it
        if 'Total Enrolments' not in df.columns and all(col in df.columns for col in ['Boys', 'Girls']):
            df['Total Enrolments'] = df['Boys'] + df['Girls']
        
        # Select required columns
        return super().transform_data(df)


class NationalResultsProcessor(BaseExcelProcessor):
    """Process NAPLAN national results data from Excel to CSV format."""
    
    @property
    def required_columns(self) -> List[str]:
        return [
            'Calendar Year', 'School Name', 'State', 
            'Reading Mean', 'Writing Mean', 'Spelling Mean', 
            'Grammar Mean', 'Numeracy Mean'
        ]


class SchoolProfileProcessor(BaseExcelProcessor):
    """Process school profile data from Excel to CSV format."""
    
    @property
    def required_columns(self) -> List[str]:
        return [
            'Calendar Year', 'School Name', 'State', 'School Sector', 
            'School Type', 'ICSEA Value'
        ]


class ProcessorFactory:
    """Factory for creating the appropriate processor based on file type."""
    
    @staticmethod
    def get_processor(file_type: str, data_dir: str = None) -> BaseExcelProcessor:
        """
        Get the appropriate processor for the given file type.
        
        Args:
            file_type (str): Type of file to process
            data_dir (str, optional): Base directory for data files
            
        Returns:
            BaseExcelProcessor: Appropriate processor for the file type
            
        Raises:
            ValueError: If the file type is not supported
        """
        processors = {
            'location': SchoolLocationProcessor,
            'enrolment': EnrolmentProcessor,
            'results': NationalResultsProcessor,
            'profile': SchoolProfileProcessor
        }
        
        if file_type.lower() not in processors:
            raise ValueError(f"Unsupported file type: {file_type}. Supported types: {list(processors.keys())}")
        
        return processors[file_type.lower()](data_dir)
    
    @staticmethod
    def detect_processor_from_filename(filename: str, data_dir: str = None) -> BaseExcelProcessor:
        """
        Detect the appropriate processor based on the filename.
        
        Args:
            filename (str): Name of the file to process
            data_dir (str, optional): Base directory for data files
            
        Returns:
            BaseExcelProcessor: Appropriate processor for the file
            
        Raises:
            ValueError: If the file type cannot be determined
        """
        filename = filename.lower()
        
        if 'location' in filename:
            return SchoolLocationProcessor(data_dir)
        elif 'enrolment' in filename or 'enrollment' in filename or 'grade' in filename:
            return EnrolmentProcessor(data_dir)
        elif 'naplan' in filename or 'result' in filename:
            return NationalResultsProcessor(data_dir)
        elif 'profile' in filename:
            return SchoolProfileProcessor(data_dir)
        else:
            raise ValueError(f"Could not determine processor type from filename: {filename}")


def main():
    """Main entry point for command line usage."""
    # Example 1: Process school location data
    location_processor = ProcessorFactory.get_processor('location')
    location_processor.process_data(
        input_file="School Location 2024.xlsx",
        output_file="school_locations.json"
    )
    
    # Example 2: Process using filename detection
    # processor = ProcessorFactory.detect_processor_from_filename("School Profile 2024.xlsx")
    # processor.process_data(
    #     input_file="School Profile 2024.xlsx",
    #     output_file="school_profiles.json"
    # )


if __name__ == "__main__":
    main()
