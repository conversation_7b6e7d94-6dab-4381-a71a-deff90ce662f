import requests
from bs4 import BeautifulSoup
import csv
import json
import pandas as pd
from difflib import get_close_matches
from shapely.geometry import shape, Point
from pathlib import Path
from typing import List, Dict, Any, Optional

class SchoolDataCrawler:
    """Crawler for extracting and processing school ranking data."""
    
    def __init__(self, data_dir: str = None):
        """
        Initialize the crawler with data directory paths.
        
        Args:
            data_dir (str): Base directory for data files. If None, uses data_processor/data/
        """
        if data_dir is None:
            data_dir = Path(__file__).parent.parent.parent / 'data'
        self.data_dir = Path(data_dir)
        self.raw_dir = self.data_dir / 'raw'
        self.processed_dir = self.data_dir / 'processed'
        
        # Create directories if they don't exist
        self.raw_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)

        self.headers = [
            "order", "school_name", "suburb", "state", "postcode",
            "state overall score", "better_education_percentile",
            "english", "maths", "total enrolments", "sector", "ses"
        ]

    def crawl_school_rankings(self, url: str) -> List[List[str]]:
        """
        Crawl school rankings from the specified URL.
        
        Args:
            url (str): URL to crawl for school rankings
            
        Returns:
            List[List[str]]: Extracted school data
        """
        response = requests.get(url)
        soup = BeautifulSoup(response.content, "html.parser")
        
        table = soup.find("table", {"class": "table table-striped table-bordered table-hover"})
        if not table:
            raise ValueError("Table not found on the webpage")

        data = []
        for row in table.find_all("tr")[1:]:  # Skip header row
            cols = []
            for col in row.find_all("td"):
                if col.find("img"):
                    img = col.find("img")
                    if img:
                        src = img["src"]
                        if "book" in src:
                            cols.append(src.split("/")[-1].replace("book", "").replace(".png", ""))
                        elif "medal_gold" in src:
                            cols.append(src.split("/")[-1].replace("medal_gold", "").replace(".png", ""))
                elif "Trend / Compare" not in col.text.strip():
                    if len(cols) == 0 and col.text.strip() == '':  # order column
                        cols.append(0)
                    elif len(cols) == 1:  # school_name column
                        parts = col.text.strip().split(',')
                        if len(parts) == 4:
                            school, suburb, state = parts[0].strip(), parts[1].strip(), parts[2].strip()
                            cols.extend([school, suburb, state])
                        else:
                            cols.extend([col.text.strip(), None, None])
                    else:
                        cols.append(col.text.strip())
            data.append(cols)
        
        return data

    def process_rankings(self, data: List[List[str]], locations_file: str) -> List[Dict[str, Any]]:
        """
        Process and enrich ranking data with location information.
        
        Args:
            data: List of school ranking data
            locations_file: Path to the school locations CSV file
            
        Returns:
            List[Dict[str, Any]]: Processed school data with locations
        """
        # Update ranking_percentile header
        if "better_education_percentile" in self.headers:
            self.headers[self.headers.index("better_education_percentile")] = "ranking_percentile"

        # Filter for government schools
        data = [row for row in data if row[self.headers.index('sector')].lower() == 'government']

        # Load and filter locations data
        locations_df = pd.read_csv(self.processed_dir / locations_file)
        locations_df = locations_df[
            (locations_df['School Sector'].str.lower() == 'government') & 
            (locations_df['State'].str.upper() == 'VIC') & 
            (locations_df['School Type'].isin(['Primary', 'Combined']))
        ]
        
        locations_dict = locations_df.set_index('School Name')[['Latitude', 'Longitude']].to_dict('index')

        # Add locations to data
        for row in data:
            school_name = row[self.headers.index("school_name")]
            postcode = row[self.headers.index("postcode")]
            
            if school_name in locations_dict:
                row.extend([locations_dict[school_name]['Latitude'], locations_dict[school_name]['Longitude']])
            else:
                # Try matching by postcode
                filtered_locations = locations_df[
                    locations_df['Postcode'].astype(str) == str(postcode)
                ].set_index('School Name')[['Latitude', 'Longitude']].to_dict('index')
                
                if filtered_locations:
                    close_matches = get_close_matches(school_name, filtered_locations.keys(), n=1, cutoff=0.6)
                    if close_matches:
                        closest_match = close_matches[0]
                        row.extend([
                            filtered_locations[closest_match]['Latitude'],
                            filtered_locations[closest_match]['Longitude']
                        ])
                        print(f"Using closest match for {school_name} in postcode {postcode}: {closest_match}")
                    else:
                        print(f"Unable to find location for {school_name} in postcode {postcode}")
                        row.extend([None, None])
                else:
                    print(f"No schools found in postcode {postcode} for {school_name}")
                    row.extend([None, None])

        # Update headers and filter columns
        self.headers.extend(['latitude', 'longitude'])
        columns_to_keep = ["school_name", "postcode", "ranking_percentile", "latitude", "longitude"]
        indices = [self.headers.index(col) for col in columns_to_keep]
        
        filtered_data = [[row[i] for i in indices] for row in data]
        
        # Convert to list of dicts
        return [
            {header: row[i] for i, header in enumerate(columns_to_keep)}
            for row in filtered_data
        ]

    def add_zone_boundaries(self, schools: List[Dict[str, Any]], geojson_file: str) -> List[Dict[str, Any]]:
        """
        Add school zone boundaries from GeoJSON file to school data.
        
        Args:
            schools: List of school dictionaries
            geojson_file: Path to the GeoJSON file containing zone boundaries
            
        Returns:
            List[Dict[str, Any]]: School data enriched with zone boundaries
        """
        try:
            with open(self.processed_dir / geojson_file, "r", encoding="utf-8") as f:
                geojson_data = json.load(f)
        except FileNotFoundError:
            print(f"GeoJSON file not found: {geojson_file}")
            return schools

        for school in schools:
            lat = school.get("latitude")
            lon = school.get("longitude")
            if lat is not None and lon is not None:
                try:
                    lat = float(lat)
                    lon = float(lon)
                except ValueError:
                    school["coordinates"] = None
                    continue
                
                point = Point(lon, lat)
                matched = False
                for feature in geojson_data.get("features", []):
                    polygon = shape(feature["geometry"])
                    if polygon.contains(point):
                        school["coordinates"] = feature["geometry"]["coordinates"]
                        matched = True
                        break
                if not matched:
                    school["coordinates"] = None
        
        return schools

    def save_data(self, data: List[Dict[str, Any]], base_filename: str) -> None:
        """
        Save processed data to CSV and JSON files.
        
        Args:
            data: List of school dictionaries to save
            base_filename: Base name for output files (without extension)
        """
        # Save CSV
        headers = list(data[0].keys())
        rows = [[school[header] for header in headers] for school in data]
        
        csv_path = self.processed_dir / f"{base_filename}.csv"
        with open(csv_path, "w", newline="", encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(headers)
            writer.writerows(rows)

        # Save JSON
        json_path = self.processed_dir / f"{base_filename}.json"
        with open(json_path, "w", encoding='utf-8') as f:
            json.dump(data, f, indent=4)

        print("Data saved successfully:")
        print(f"- {csv_path}")
        print(f"- {json_path}")

def main():
    """Main entry point for the crawler."""
    url = "https://bettereducation.com.au/school/Primary/vic/melbourne_top_primary_schools.aspx"
    
    crawler = SchoolDataCrawler()
    
    # Crawl and process data
    raw_data = crawler.crawl_school_rankings(url)
    
    # Save temporary data for debugging
    with open(crawler.processed_dir / "school_ranking_data_tmp.csv", "w", newline="", encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(crawler.headers)
        writer.writerows(raw_data)
    
    # Process rankings with location data
    processed_data = crawler.process_rankings(raw_data, "school_locations.csv")
    
    # Add zone boundaries
    enriched_data = crawler.add_zone_boundaries(processed_data, "Primary_integrated_2024.geojson")
    
    # Save final data
    crawler.save_data(enriched_data, "school_ranking_data")

if __name__ == "__main__":
    main()
