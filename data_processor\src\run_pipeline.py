#!/usr/bin/env python3
"""
Main script to run the data processing pipeline.
"""

import argparse
import sys
import os
from pathlib import Path
from src.processors.pipeline import DataPipeline
from src.processors.data_processor import ProcessorFactory

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='School data processing pipeline')
    
    # Add subparsers for different commands
    subparsers = parser.add_subparsers(dest='command', help='Command to run')
    
    # Process all files command
    process_all_parser = subparsers.add_parser('process-all', help='Process all Excel files')
    process_all_parser.add_argument('--no-backup', action='store_true', help='Disable backup of existing processed files')
    
    # Process single file command
    process_parser = subparsers.add_parser('process', help='Process a single Excel file')
    process_parser.add_argument('file', help='Excel file to process (relative to raw data directory)')
    process_parser.add_argument('--output', help='Output file name (relative to processed data directory)')
    process_parser.add_argument('--type', choices=['location', 'enrolment', 'results', 'profile'], 
                               help='Type of data to process (if not specified, will be detected from filename)')
    
    # Merge command
    merge_parser = subparsers.add_parser('merge', help='Merge processed JSON files')
    merge_parser.add_argument('--output', default='merged_school_data.json', 
                             help='Output file name (relative to processed data directory)')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export processed data to another JSON format')
    export_parser.add_argument('--input', default='merged_school_data.json', 
                              help='Input JSON file (relative to processed data directory)')
    export_parser.add_argument('--output', default='school_data.json', 
                              help='Output JSON file (relative to processed data directory)')
    
    # Full pipeline command
    subparsers.add_parser('full', help='Run the full pipeline (process all, merge, export)')
    
    # List files command
    subparsers.add_parser('list', help='List available raw and processed files')
    
    return parser.parse_args()

def main():
    """Main entry point."""
    args = parse_args()
    
    # Create pipeline
    pipeline = DataPipeline()
    
    if args.command == 'process-all':
        # Process all Excel files
        print("Processing all Excel files...")
        results = pipeline.process_all_excel_files(backup=not args.no_backup)
        
        # Print results
        print("\nProcessing results:")
        for input_file, output_file in results.items():
            print(f"  {input_file} -> {output_file}")
    
    elif args.command == 'process':
        # Process a single Excel file
        input_file = args.file
        
        # Determine output file name if not specified
        if args.output:
            output_file = args.output
        else:
            output_file = Path(input_file).with_suffix('.json').name
        
        print(f"Processing {input_file} to {output_file}...")
        
        try:
            # Get processor
            if args.type:
                processor = ProcessorFactory.get_processor(args.type)
            else:
                processor = ProcessorFactory.detect_processor_from_filename(input_file)
            
            # Process the file
            processor.process_data(input_file=input_file, output_file=output_file)
            
            print(f"Successfully processed {input_file} to {output_file}")
            
        except Exception as e:
            print(f"Error processing {input_file}: {str(e)}")
            sys.exit(1)
    
    elif args.command == 'merge':
        # Merge processed JSON files
        print(f"Merging processed JSON files to {args.output}...")
        
        try:
            merged_df = pipeline.merge_processed_data(output_file=args.output)
            print(f"Successfully merged {len(merged_df)} records to {args.output}")
            
        except Exception as e:
            print(f"Error merging files: {str(e)}")
            sys.exit(1)
    
    elif args.command == 'export':
        # Export processed data to another JSON format
        print(f"Exporting {args.input} to {args.output}...")
        
        try:
            pipeline.export_to_json(input_file=args.input, output_file=args.output)
            print(f"Successfully exported {args.input} to {args.output}")
            
        except Exception as e:
            print(f"Error exporting to JSON: {str(e)}")
            sys.exit(1)
    
    elif args.command == 'full':
        # Run the full pipeline
        print("Running full pipeline...")
        
        # Process all Excel files
        print("\n1. Processing all Excel files...")
        results = pipeline.process_all_excel_files(backup=True)
        
        # Print processing results
        print("\nProcessing results:")
        for input_file, output_file in results.items():
            print(f"  {input_file} -> {output_file}")
        
        # Merge processed JSON files
        print("\n2. Merging processed JSON files...")
        try:
            merged_df = pipeline.merge_processed_data()
            print(f"Successfully merged {len(merged_df)} records")
            
        except Exception as e:
            print(f"Error merging files: {str(e)}")
            print("Continuing with export step...")
        
        # Export to another JSON format
        print("\n3. Exporting to another JSON format...")
        try:
            pipeline.export_to_json()
            print("Successfully exported to JSON")
            
        except Exception as e:
            print(f"Error exporting to JSON: {str(e)}")
            sys.exit(1)
        
        print("\nFull pipeline completed successfully!")
    
    elif args.command == 'list':
        # List available raw and processed files
        print("Raw data files:")
        for file in sorted(pipeline.raw_dir.glob('**/*.xlsx')):
            rel_path = file.relative_to(pipeline.raw_dir)
            print(f"  {rel_path}")
        
        print("\nProcessed data files:")
        for file in sorted(pipeline.processed_dir.glob('*.json')):
            rel_path = file.relative_to(pipeline.processed_dir)
            print(f"  {rel_path}")
    
    else:
        # No command specified, print help
        print("No command specified. Use --help for usage information.")
        sys.exit(1)

if __name__ == "__main__":
    main()
