{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Current File with Arguments",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "args": [
                "${command:pickArgs}"
            ]
        },
        {
            "name": "Run pipeline: School Location 2024.xlsx",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/data_processor/src/run_pipeline.py",
            "cwd": "${workspaceFolder}",
            "env": {
              "PYTHONPATH": "${workspaceFolder}/data_processor"
            },
            "console": "integratedTerminal",
            "args": [
                "process",
                "School Location 2024.xlsx",
                "--output",
                "school_location_2024.json"
            ]
        }
    ]
}