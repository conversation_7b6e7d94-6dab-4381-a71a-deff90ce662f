#!/usr/bin/env python3
"""
Test script for the data processor classes.
"""

from src.processors.data_processor import SchoolLocationProcessor

def main():
    """Test the SchoolLocationProcessor class."""
    print("Testing SchoolLocationProcessor...")
    
    # Create a processor
    processor = SchoolLocationProcessor()
    
    # Print the processor's attributes
    print(f"Data directory: {processor.data_dir}")
    print(f"Raw directory: {processor.raw_dir}")
    print(f"Processed directory: {processor.processed_dir}")
    print(f"Required columns: {processor.required_columns}")
    print(f"Sheet name: {processor.sheet_name}")
    
    # List files in the raw directory
    print("\nFiles in raw directory:")
    for file in processor.raw_dir.glob("**/*.xlsx"):
        print(f"  {file.relative_to(processor.raw_dir)}")
    
    # List files in the processed directory
    print("\nFiles in processed directory:")
    for file in processor.processed_dir.glob("**/*.json"):
        print(f"  {file.relative_to(processor.processed_dir)}")
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    main()
