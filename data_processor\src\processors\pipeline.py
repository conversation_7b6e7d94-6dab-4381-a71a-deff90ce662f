import os
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
import pandas as pd
from .data_processor import ProcessorFactory, BaseExcelProcessor

class DataPipeline:
    """
    Coordinates the processing of multiple data sources.
    """
    
    def __init__(self, data_dir: str = None):
        """
        Initialize the pipeline with data directory paths.
        
        Args:
            data_dir (str): Base directory for data files. If None, uses data_processor/data/
        """
        if data_dir is None:
            data_dir = Path(__file__).parent.parent.parent / 'data'
        self.data_dir = Path(data_dir)
        self.raw_dir = self.data_dir / 'raw'
        self.processed_dir = self.data_dir / 'processed'
        
        # Create directories if they don't exist
        self.raw_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
    
    def process_all_excel_files(self, backup: bool = True) -> Dict[str, str]:
        """
        Process all Excel files in the raw data directory.
        
        Args:
            backup (bool): Whether to create backups of existing processed files
            
        Returns:
            Dict[str, str]: Dictionary mapping input files to output files
        """
        results = {}
        
        # Get all Excel files in the raw directory
        excel_files = list(self.raw_dir.glob('**/*.xlsx'))
        
        print(f"Found {len(excel_files)} Excel files to process")
        
        for excel_file in excel_files:
            try:
                # Get the relative path from the raw directory
                rel_path = excel_file.relative_to(self.raw_dir)
                
                # Skip files in subdirectories for now (can be enhanced later)
                if len(rel_path.parts) > 1:
                    print(f"Skipping file in subdirectory: {rel_path}")
                    continue
                
                # Get the filename
                filename = rel_path.name
                
                # Create output filename (replace .xlsx with .json)
                output_filename = filename.replace('.xlsx', '.json')
                
                # Create backup if requested and output file exists
                output_path = self.processed_dir / output_filename
                if backup and output_path.exists():
                    backup_path = self.processed_dir / f"{output_filename}_backup.json"
                    print(f"Creating backup of {output_path} to {backup_path}")
                    import shutil
                    shutil.copy2(output_path, backup_path)
                
                # Detect the appropriate processor
                try:
                    processor = ProcessorFactory.detect_processor_from_filename(filename)
                    
                    # Process the file
                    print(f"\nProcessing {filename}...")
                    processor.process_data(
                        input_file=str(rel_path),
                        output_file=output_filename
                    )
                    
                    # Add to results
                    results[str(rel_path)] = output_filename
                    
                except ValueError as e:
                    print(f"Skipping {filename}: {str(e)}")
                    continue
                
            except Exception as e:
                print(f"Error processing {excel_file}: {str(e)}")
                continue
        
        return results
    
    def merge_processed_data(self, output_file: str = "merged_school_data.json") -> pd.DataFrame:
        """
        Merge all processed JSON files into a single DataFrame.
        
        Args:
            output_file (str): Name of the output file
            
        Returns:
            pd.DataFrame: Merged DataFrame
        """
        # Get all JSON files in the processed directory
        json_files = list(self.processed_dir.glob('*.json'))
        
        # Skip backup files
        json_files = [f for f in json_files if not f.name.endswith('_backup.json')]
        
        if not json_files:
            raise ValueError("No processed JSON files found to merge")
        
        print(f"Found {len(json_files)} JSON files to merge")
        
        # Read all JSON files into DataFrames
        dfs = []
        for json_file in json_files:
            try:
                df = pd.read_json(json_file, orient='records')
                # Add source filename as a column
                df['data_source'] = json_file.name
                dfs.append(df)
                print(f"Read {len(df)} rows from {json_file.name}")
            except Exception as e:
                print(f"Error reading {json_file}: {str(e)}")
                continue
        
        if not dfs:
            raise ValueError("No CSV files could be read successfully")
        
        # Merge all DataFrames on common columns
        # First, identify common columns across all DataFrames
        common_columns = set(dfs[0].columns)
        for df in dfs[1:]:
            common_columns &= set(df.columns)
        
        # Remove 'data_source' from common columns
        if 'data_source' in common_columns:
            common_columns.remove('data_source')
        
        # Ensure we have at least 'School Name' and 'Calendar Year' as common columns
        required_common = {'School Name', 'Calendar Year'}
        if not required_common.issubset(common_columns):
            missing = required_common - common_columns
            raise ValueError(f"Missing required common columns for merging: {missing}")
        
        print(f"Common columns for merging: {common_columns}")
        
        # Merge DataFrames
        merged_df = dfs[0]
        for df in dfs[1:]:
            merged_df = pd.merge(
                merged_df, 
                df, 
                on=list(common_columns),
                how='outer',
                suffixes=('', f'_{df["data_source"].iloc[0]}')
            )
        
        # Save merged DataFrame to JSON
        output_path = self.processed_dir / output_file
        
        # Ensure all column names are lowercase with underscores
        merged_df.columns = [col.lower().replace(' ', '_') for col in merged_df.columns]
        
        merged_df.to_json(output_path, orient='records', indent=4)
        
        print(f"Merged data saved to {output_path}")
        print(f"Merged data shape: {merged_df.shape}")
        
        return merged_df
    
    def export_to_json(self, input_file: str = "merged_school_data.json", output_file: str = "school_data.json") -> None:
        """
        Export a processed JSON file to another JSON format (with potential transformations).
        
        Args:
            input_file (str): Name of the input JSON file
            output_file (str): Name of the output JSON file
        """
        input_path = self.processed_dir / input_file
        output_path = self.processed_dir / output_file
        
        if not input_path.exists():
            raise ValueError(f"Input file not found: {input_path}")
        
        # Read JSON file
        df = pd.read_json(input_path, orient='records')
        
        # Ensure all column names are lowercase with underscores
        df.columns = [col.lower().replace(' ', '_') for col in df.columns]

        # Convert specific columns to appropriate types
        if 'school_age_id' in df.columns:
            try:
                df['school_age_id'] = df['school_age_id'].astype(int)
                print(f"Converted 'school_age_id' column to integer type")
            except ValueError as e:
                print(f"Warning: Could not convert 'school_age_id' column to integer: {e}")
                
        # Convert to JSON
        df.to_json(output_path, orient='records', indent=4)
        
        print(f"Exported {len(df)} records from {input_path} to {output_path}")


def main():
    """Main entry point for command line usage."""
    pipeline = DataPipeline()
    
    # Process all Excel files
    print("Processing all Excel files...")
    results = pipeline.process_all_excel_files(backup=True)
    
    # Print results
    print("\nProcessing results:")
    for input_file, output_file in results.items():
        print(f"  {input_file} -> {output_file}")
    
    # Optionally merge processed data
    # print("\nMerging processed data...")
    # merged_df = pipeline.merge_processed_data()
    
    # Optionally export to another JSON format
    # print("\nExporting to another JSON format...")
    # pipeline.export_to_json()


if __name__ == "__main__":
    main()
