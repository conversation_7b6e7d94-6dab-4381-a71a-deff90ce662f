# SchoolZone Project

SchoolZone is a data-driven web application that aggregates, processes, and visualizes comprehensive information about public primary schools in Victoria, Australia.

## Project Structure

The project is organized into two main components:

### 1. Data Processor (`/data_processor`)
Handles data collection, processing, and management:
- Web crawling for school rankings
- Data processing and validation
- Structured data storage
- Data backup handling

### 2. Web Application (`/web_app`)
Provides the user interface and visualization:
- Interactive map interface
- School rankings visualization
- School zone boundaries
- Real-time data loading

## Setup

1. Install data processor dependencies:
```bash
cd data_processor
pip install -r requirements.txt
```

2. Install web application dependencies:
```bash
cd web_app
pip install -r requirements.txt
```

## Usage

1. First, process the school data:
```bash
cd data_processor
python -m src.processors.data_processor
python -m src.crawler.web_crawler
```

2. Then run the web application:
```bash
cd web_app
python -m src.app
```

The application will be available at http://localhost:5000

## Data Sources

- School location data from official sources
- School rankings from BetterEducation.com.au
- School zone boundaries from government data

## Contributing

When contributing to this project, please follow these guidelines:
- Use snake_case for Python files and functions
- Use camelCase for HTML/JavaScript/CSS
- Keep the web app and data processor components separate
- Follow each component's specific coding standards

## Project Status

Phase 1 (Current):
- Victorian government primary schools
- School location and ranking data
- Basic visualization features

Future Development:
- Additional state coverage
- Enhanced data processing pipeline
- Advanced analytics features
